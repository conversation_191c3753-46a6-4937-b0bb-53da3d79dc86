import time
import os

def clear_screen():
    """清除命令行屏幕"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_file_content(filename, lines_per_chunk=10, delay=1):
    """分块打印文件内容
    
    Args:
        filename: 要打印的文件名
        lines_per_chunk: 每次打印的行数，默认为10
        delay: 每次打印后的等待时间(秒)，默认为1
    """
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            lines = file.readlines()
            
            for i in range(0, len(lines), lines_per_chunk):
                chunk = lines[i:i+lines_per_chunk]
                print(''.join(chunk))
                
                time.sleep(delay)
                clear_screen()
                
    except FileNotFoundError:
        print(f"错误: 文件 '{filename}' 未找到")
    except Exception as e:
        print(f"发生错误: {str(e)}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 2:
        print("使用方法: python print_file.py <文件名>")
        sys.exit(1)
    
    filename = sys.argv[1]
    print_file_content(filename)