Beginning AutoGluon training... Time limit = 600s
AutoGluon will save models to 'C:\work\aiops-challenge-2025-master\demo\autogluon-m4-hourly'
=================== System Info ===================
AutoGluon Version:  1.3.1
Python Version:     3.12.9
Operating System:   Windows
Platform Machine:   AMD64
Platform Version:   10.0.26100
CPU Count:          8
GPU Count:          0
Memory Avail:       11.16 GB / 31.96 GB (34.9%)
Disk Space Avail:   632.47 GB / 930.71 GB (68.0%)
===================================================
Setting presets to: medium_quality

Fitting with arguments:
{'enable_ensemble': True,
 'eval_metric': MASE,
 'hyperparameters': 'light',
 'known_covariates_names': [],
 'num_val_windows': 1,
 'prediction_length': 48,
 'quantile_levels': [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9],
 'random_seed': 123,
 'refit_every_n_windows': 1,
 'refit_full': False,
 'skip_model_selection': False,
 'target': 'target',
 'time_limit': 600,
 'verbosity': 2}

Inferred time series frequency: 'h'
Provided train_data has 148060 rows, 200 time series. Median time series length is 700 (min=700, max=960). 

Provided data contains following columns:
	target: 'target'

AutoGluon will gauge predictive performance using evaluation metric: 'MASE'
	This metric's sign has been flipped to adhere to being higher_is_better. The metric score can be multiplied by -1 to get the metric value.
===================================================

Starting training. Start time is 2025-07-12 13:50:42
Models that will be trained: ['Naive', 'SeasonalNaive', 'RecursiveTabular', 'DirectTabular', 'ETS', 'Theta', 'Chronos[bolt_small]', 'TemporalFusionTransformer']
Training timeseries model Naive. Training for up to 65.9s of the 593.4s of remaining time.
	-6.6629       = Validation score (-MASE)
	0.23    s     = Training runtime
	3.46    s     = Validation (prediction) runtime
Training timeseries model SeasonalNaive. Training for up to 73.7s of the 589.7s of remaining time.
	-1.2169       = Validation score (-MASE)
	0.23    s     = Training runtime
	0.26    s     = Validation (prediction) runtime
Training timeseries model RecursiveTabular. Training for up to 84.2s of the 589.2s of remaining time.
