lr: 0.001
model_kwargs:
  c_feat_dynamic_cat: []
  c_feat_static_cat:
  - 1
  c_past_feat_dynamic_cat: []
  context_length: 192
  d_feat_dynamic_real:
  - 1
  - 1
  - 1
  - 1
  - 1
  d_feat_static_real:
  - 1
  d_hidden: 32
  d_past_feat_dynamic_real:
  - 47
  d_var: 32
  distr_output: !!python/object/apply:copyreg.__newobj_ex__
    args:
    - !!python/name:gluonts.torch.distributions.quantile_output.QuantileOutput ''
    - !!python/tuple []
    - &id001 !!python/object/apply:collections.OrderedDict
      - - - quantiles
          - - 0.1
            - 0.2
            - 0.3
            - 0.4
            - 0.5
            - 0.6
            - 0.7
            - 0.8
            - 0.9
    state:
      __init_args__: *id001
      _quantiles:
      - 0.1
      - 0.2
      - 0.3
      - 0.4
      - 0.5
      - 0.6
      - 0.7
      - 0.8
      - 0.9
      args_dim:
        outputs: 9
      num_quantiles: 9
  dropout_rate: 0.1
  num_heads: 4
  prediction_length: 96
patience: 10
weight_decay: 1.0e-08
