{"__kind__": "instance", "args": [], "class": "gluonts.torch.model.predictor.PyTorchPredictor", "kwargs": {"batch_size": 64, "device": "auto", "forecast_generator": {"__kind__": "instance", "args": [], "class": "gluonts.model.forecast_generator.DistributionForecastGenerator", "kwargs": {"distr_output": {"__kind__": "instance", "args": [], "class": "gluonts.torch.distributions.studentT.StudentTOutput", "kwargs": {"beta": 0.0}}}}, "input_names": ["past_target", "past_observed_values", "past_time_feat", "future_time_feat"], "input_transform": {"__kind__": "instance", "class": "gluonts.transform._base.Chain", "kwargs": {"transformations": [{"__kind__": "instance", "args": [], "class": "gluonts.transform.field.SelectFields", "kwargs": {"allow_missing": true, "input_fields": ["item_id", "info", "start", "target", "feat_dynamic_real"]}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.field.RenameFields", "kwargs": {"mapping": {"feat_dynamic_real": "time_feat"}}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.feature.AddObservedValuesIndicator", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "imputation_method": {"__kind__": "instance", "args": [], "class": "gluonts.transform.feature.DummyValueImputation", "kwargs": {"dummy_value": 0.0}}, "output_field": "observed_values", "target_field": "target"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.split.InstanceSplitter", "kwargs": {"dummy_value": 0.0, "forecast_start_field": "forecast_start", "future_length": 5, "instance_sampler": {"__kind__": "instance", "class": "gluonts.transform.sampler.PredictionSplitSampler", "kwargs": {"allow_empty_interval": false, "axis": -1, "min_future": 0, "min_past": 0}}, "is_pad_field": "is_pad", "lead_time": 0, "output_NTC": true, "past_length": 96, "start_field": "start", "target_field": "target", "time_series_fields": ["observed_values", "time_feat"]}}]}}, "lead_time": 0, "output_transform": null, "prediction_length": 5, "prediction_net": {"__kind__": "instance", "args": [], "class": "gluonts.torch.model.patch_tst.lightning_module.PatchTSTLightningModule", "kwargs": {"lr": 0.001, "model_kwargs": {"activation": "relu", "context_length": 96, "d_model": 32, "dim_feedforward": 128, "distr_output": {"__kind__": "instance", "args": [], "class": "gluonts.torch.distributions.studentT.StudentTOutput", "kwargs": {"beta": 0.0}}, "dropout": 0.1, "nhead": 4, "norm_first": false, "num_encoder_layers": 2, "num_feat_dynamic_real": 1, "padding_patch": "end", "patch_len": 16, "prediction_length": 5, "scaling": "mean", "stride": 8}, "weight_decay": 1e-08}}}}