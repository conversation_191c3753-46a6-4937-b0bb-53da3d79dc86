# 导入必要的库和模块
import argparse
import datetime
import os
from typing import Callable, Dict, List

import numpy as np
import pandas as pd

# 导入项目配置和数据处理相关模块
from aiops_challenge_2025 import config
from aiops_challenge_2025.data import DataHelper, fillna, resample_data
from aiops_challenge_2025.experiment import (
    DATA_INTERVAL,  # 数据采样间隔
    PREDICT_NUM,  # 预测数据点数量
    PREDICT_WINDOW,  # 预测窗口大小
    ModelInterface,  # 模型接口基类
    ChangeInfo,  # 变更信息类
    prev_quarter_time,  # 获取前一季度时间的函数
)


class SimpleWeightedImplementation(ModelInterface):
    """
    简单加权实现模型类
    继承自ModelInterface，实现基于历史数据的简单预测和决策功能
    """

    def __init__(self, weights: List[float] = None):
        """
        构造函数

        Args:
            weights: 数字数组，用于加权计算的权重参数
        """
        self.weights = weights if weights is not None else [1.0]
        # print
        print("weights:", self.weights)
        # print n-days
        print("n-days:", len(self.weights))
        weights_array = np.array(self.weights)
        self.weights_normalized = weights_array / weights_array.sum()  # 归一化权重
        # 打印 weights_normalized
        print("weights_normalized:", self.weights_normalized)

    def fit(self, data_dirs: List[str]):
        """
        模型训练方法

        Args:
            data_dirs: 数据目录列表，包含训练数据的路径
        """
        # 创建数据助手，用于加载和管理数据
        data_helper = DataHelper(
            data_dirs=data_dirs,
            # 这里只加载小区级数据。如果考虑邻区数据，则需在列表中加入对应配置或不指定 feature_groups
            feature_groups=[
                config.FEATURE_GROUP_KPI,  # KPI特征组
                # config.FEATURE_GROUP_EP,   # EP特征组（已注释）
                # config.FEATURE_GROUP_NRM,  # NRM特征组（已注释）
            ],
        )
        # 打印所有小区ID
        print(data_helper.cells)
        # 遍历每个小区的数据，打印基本信息（仅处理第一个小区后跳出）
        for cell_id in data_helper.cells:
            for feature_group, data in data_helper.get(cell_id).items():
                print(cell_id, feature_group.data_id, type(data))
            break

    def predict(
            self,
            task: ChangeInfo,
            data_getter: Callable[[str], Dict[config.FeatureGroup, pd.DataFrame]],
    ) -> pd.DataFrame:
        """
        预测方法：基于历史KPI数据进行加权平均预测

        算法逻辑：
        1. 根据weights的长度n，获取训练数据最近n天的数据
        2. 每天的数据量对应一个PREDICT_WINDOW (96个数据点，即24小时)
        3. 对每天的数据进行填充缺失值和重新采样，确保时间对齐
        4. 将最近n天的数据按天分组，每天96个数据点
        5. 对相同时间点的不同天数据进行加权平均
        6. 权重顺序：weights[0]=最近1天权重, weights[1]=最近2天权重, ..., weights[n-1]=最近n天权重

        Args:
            task: 变更任务信息，包含小区ID和开始时间
            data_getter: 数据获取函数，根据小区ID返回特征组数据字典

        Returns:
            预测结果DataFrame，包含未来PREDICT_NUM个时间点的预测值
        """
        # 获取指定小区的所有特征组数据
        data = data_getter(task.cell_id)

        # 数据泄露检查：确保所有数据的时间戳都在任务开始时间之前
        for item in data.values():
            assert (item.index < task.start_time).all(), "待预测数据泄露！"

        # 提取KPI特征组数据
        item = data[config.FEATURE_GROUP_KPI]

        # 获取权重数组长度，确定需要使用的天数
        assert len(self.weights) > 0, "权重数组不能为空"
        n_days = len(self.weights)

        # 计算结束时间（任务开始时间前一个数据间隔）
        train_end_time = task.start_time - DATA_INTERVAL
        train_start_time = min(prev_quarter_time(item.index.min()), train_end_time - PREDICT_WINDOW * n_days)
        item = resample_data(
            item.astype(float), start=train_start_time, end=train_end_time, interval=DATA_INTERVAL
        )
        item, non_missing = fillna(item)

        # 确保有足够的数据进行加权计算
        total_needed_points = PREDICT_NUM * n_days
        assert len(item) > total_needed_points, f"数据不足，需要 {total_needed_points} 个数据点，实际只有 {len(item)} 个"

        # 按照权重对最近n_days天的数据进行加权平均
        # 最近1天
        ret = item.iloc[-PREDICT_NUM:] * self.weights_normalized[0]
        # 最近2天到n_days天
        for i in range(2, n_days + 1):
            tmp = item.iloc[-PREDICT_NUM * i: -PREDICT_NUM * (i - 1)] * self.weights_normalized[i - 1]
            ret += tmp.values

        # 设置正确的时间索引
        ret.index = (
                train_end_time + pd.RangeIndex(start=1, stop=len(ret) + 1) * DATA_INTERVAL
        )
        ret.index.name = config.FEATURE_GROUP_KPI.time_column
        return ret

    def decide(
            self,
            cell_id: str,
            start_time: datetime.datetime,
            target: pd.DataFrame,
            data_getter: Callable[[str], Dict[config.FeatureGroup, pd.DataFrame]],
    ) -> Dict[str, float]:
        """
        决策方法：基于NRM数据进行参数调整决策

        Args:
            cell_id: 小区ID
            start_time: 开始时间
            target: 目标数据框
            data_getter: 数据获取函数

        Returns:
            参数调整字典，键为参数名，值为调整值
        """
        # 获取指定小区的所有特征组数据
        data = data_getter(cell_id)

        # 数据泄露检查：确保所有数据的时间戳都在开始时间之前
        for item in data.values():
            assert (item.index < start_time).all(), "数据泄露！"

        # 提取NRM特征组数据并转换为浮点型
        item = data[config.FEATURE_GROUP_NRM].astype(float)

        # 前向填充缺失值并取最后一行数据
        row = item.ffill().iloc[-1]

        # 将Series转换为字典返回
        return dict(zip(row.index, row.values))


CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

# 创建模型实例，传入默认的权重数组, 参数含义为最近1天的权重，因为考虑到周周期，因此七天前的权重最高，同时最近的日期权重约高，
# 因此权重数组的顺序为：最近1天、最近2天、最近3天、最近4天、最近5天、最近6天、最近7天。
model_1 = SimpleWeightedImplementation(weights=[1.0])
model_0_1 = SimpleWeightedImplementation(weights=[0.0, 1.0])
model_1_1 = SimpleWeightedImplementation(weights=[1.0, 1.0])
model_1_1_1 = SimpleWeightedImplementation(weights=[1.0, 1.0, 1.0])
model_1_1_1_1 = SimpleWeightedImplementation(weights=[1.0, 1.0, 1.0, 1.0])

model_7 = SimpleWeightedImplementation(weights=[.0, .0, .0, .0, .0, .0, 1.0])
model_week = SimpleWeightedImplementation(weights=[32.0, 16.0, 8.0, 4.0, 2.0, 1.0, 64.0])
model = SimpleWeightedImplementation(weights=[32.0, 16.0, 8.0, 4.0, 2.0, 1.0, 64.0])

# model = SimpleWeightedImplementation(weights=[32.0, 16.0, 8.0, 4.0, 2.0, 1.0, 64.0])
# model = SimpleWeightedImplementation(weights=[32.0, 16.0, 8.0, 4.0, 2.0, 1.0, 64.0])


def _main():
    parser = argparse.ArgumentParser()
    sample_data_dir = os.path.join(os.path.dirname(config.CURRENT_DIR), "sample")
    parser.add_argument(
        "-d",
        "--data-dir",
        action="append",
        default=[],
        required=True,
        dest="data_dirs",
        help=f"数据路径，目录结构形如 {sample_data_dir}。可指定多个。",
    )
    args = parser.parse_args()

    model.fit(args.data_dirs)


if __name__ == "__main__":
    _main()
