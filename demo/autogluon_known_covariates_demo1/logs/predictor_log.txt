Beginning AutoGluon training... Time limit = 600s
AutoGluon will save models to 'C:\work\aiops-challenge-2025-master\demo\autogluon_known_covariates_demo1'
=================== System Info ===================
AutoGluon Version:  1.3.1
Python Version:     3.12.9
Operating System:   Windows
Platform Machine:   AMD64
Platform Version:   10.0.26100
CPU Count:          8
GPU Count:          0
Memory Avail:       9.02 GB / 31.96 GB (28.2%)
Disk Space Avail:   629.23 GB / 930.71 GB (67.6%)
===================================================
Setting presets to: best_quality

Fitting with arguments:
{'enable_ensemble': True,
 'eval_metric': MASE,
 'hyperparameters': 'default',
 'known_covariates_names': ['x'],
 'num_val_windows': 2,
 'prediction_length': 5,
 'quantile_levels': [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9],
 'random_seed': 123,
 'refit_every_n_windows': 1,
 'refit_full': False,
 'skip_model_selection': False,
 'target': 'target',
 'time_limit': 600,
 'verbosity': 2}

Inferred time series frequency: 'D'
Provided train_data has 30 rows, 2 time series. Median time series length is 15 (min=15, max=15). 
Time series in train_data are too short for chosen num_val_windows=2. Reducing num_val_windows to 1.

Provided data contains following columns:
	target: 'target'
	known_covariates:
		categorical:        []
		continuous (float): ['x']

To learn how to fix incorrectly inferred types, please see documentation for TimeSeriesPredictor.fit

AutoGluon will gauge predictive performance using evaluation metric: 'MASE'
	This metric's sign has been flipped to adhere to being higher_is_better. The metric score can be multiplied by -1 to get the metric value.
===================================================

Starting training. Start time is 2025-07-12 14:33:03
Models that will be trained: ['SeasonalNaive', 'RecursiveTabular', 'DirectTabular', 'NPTS', 'DynamicOptimizedTheta', 'AutoETS', 'ChronosZeroShot[bolt_base]', 'ChronosFineTuned[bolt_small]', 'TemporalFusionTransformer', 'DeepAR', 'PatchTST', 'TiDE']
Training timeseries model SeasonalNaive. Training for up to 46.1s of the 599.9s of remaining time.
	-0.7043       = Validation score (-MASE)
	0.02    s     = Training runtime
	0.02    s     = Validation (prediction) runtime
Training timeseries model RecursiveTabular. Training for up to 50.0s of the 599.9s of remaining time.
	Time series in the dataset are too short for chosen differences [7]. Setting differences to [1].
	-0.7741       = Validation score (-MASE)
	0.99    s     = Training runtime
	0.35    s     = Validation (prediction) runtime
Training timeseries model DirectTabular. Training for up to 54.4s of the 598.5s of remaining time.
	-0.5291       = Validation score (-MASE)
	0.73    s     = Training runtime
	0.11    s     = Validation (prediction) runtime
Training timeseries model NPTS. Training for up to 59.8s of the 597.7s of remaining time.
	-0.7043       = Validation score (-MASE)
	0.02    s     = Training runtime
	0.02    s     = Validation (prediction) runtime
Training timeseries model DynamicOptimizedTheta. Training for up to 66.4s of the 597.6s of remaining time.
	-0.6507       = Validation score (-MASE)
	0.02    s     = Training runtime
	0.02    s     = Validation (prediction) runtime
Training timeseries model AutoETS. Training for up to 74.7s of the 597.6s of remaining time.
	-0.6089       = Validation score (-MASE)
	0.02    s     = Training runtime
	0.02    s     = Validation (prediction) runtime
Training timeseries model ChronosZeroShot[bolt_base]. Training for up to 85.4s of the 597.6s of remaining time.
	Warning: Exception caused ChronosZeroShot[bolt_base] to fail during training... Skipping this model.
	We couldn't connect to 'https://huggingface.co' to load this file, couldn't find it in the cached files and it looks like autogluon/chronos-bolt-base is not the path to a directory containing a file named config.json.
Checkout your internet connection or see how to run the library in offline mode at 'https://huggingface.co/docs/transformers/installation#offline-mode'.
Training timeseries model ChronosFineTuned[bolt_small]. Training for up to 99.5s of the 597.0s of remaining time.
	Warning: Exception caused ChronosFineTuned[bolt_small] to fail during training... Skipping this model.
	We couldn't connect to 'https://huggingface.co' to load this file, couldn't find it in the cached files and it looks like autogluon/chronos-bolt-small is not the path to a directory containing a file named config.json.
Checkout your internet connection or see how to run the library in offline mode at 'https://huggingface.co/docs/transformers/installation#offline-mode'.
Training timeseries model TemporalFusionTransformer. Training for up to 119.3s of the 596.4s of remaining time.
