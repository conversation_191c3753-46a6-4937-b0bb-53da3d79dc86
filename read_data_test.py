from aiops_challenge_2025 import config
from aiops_challenge_2025.data import <PERSON><PERSON><PERSON><PERSON>, fillna, resample_data
from aiops_challenge_2025.experiment import (
    DATA_INTERVAL,
    PREDICT_NUM,
    PREDICT_WINDOW,
    ModelInterface,
    ChangeInfo,
    prev_quarter_time,
)

helper = DataHelper(
            data_dirs=['./Track2/train'],
        )

data = helper.get('0a8edc9f-91bb-349f-b569-1bd756411a88')
df = data.get(config.FEATURE_GROUP_MR)
print(len(df))
