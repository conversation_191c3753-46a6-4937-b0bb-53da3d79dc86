#!/bin/bash

# --- 脚本开始 ---

# 记录总起始时间
T_START_TOTAL=$(date +%s)

# 设置 -e 选项，如果任何命令返回非零退出状态，脚本将立即退出。
set -e

# --- 步骤 1 & 2: 参数检查与变量生成 ---
T_START_STEP12=$(date +%s)

# 1. 检查脚本参数
if [ -z "$1" ]; then
    echo "错误：缺少模型名称参数。"
    echo "用法: $0 <模型名称>"
    echo "例如: $0 simple_weighted:model"
    exit 1
fi

# 2. 根据输入参数和当前时间生成变量
model_info="$1"
model_name_for_dir=$(echo "$model_info" | tr ':' '_')
current_time=$(date +"%Y_%m_%d_%H%M")
output_dir="output_${model_name_for_dir}_${current_time}"
full_output_path="/root/teamshare/${output_dir}"

T_END_STEP12=$(date +%s)
echo "=================================================="
echo "[步骤 1 & 2] 参数检查与变量生成完成。"
echo "-> 耗时: $((T_END_STEP12 - T_START_STEP12)) 秒。"
echo "--------------------------------------------------"
echo "模型名称 (model_info): ${model_info}"
echo "生成的输出目录 (output_dir): ${output_dir}"
echo "完整输出路径: ${full_output_path}"
echo "=================================================="

# --- 步骤 3: 创建目录 ---
T_START_STEP3=$(date +%s)

echo "正在创建目录: ${full_output_path}"
mkdir -p "${full_output_path}"

T_END_STEP3=$(date +%s)
echo "[步骤 3] 目录创建成功。"
echo "-> 耗时: $((T_END_STEP3 - T_START_STEP3)) 秒。"
echo ""

# --- 步骤 4: 运行Python实验脚本 ---
PYTHON_COMMAND=(
    python -m aiops_challenge_2025.experiment
    -d /root/2025CCF国际AIOps挑战赛-赛道二/train/
    -t /root/2025CCF国际AIOps挑战赛-赛道二/changes-test_1-predict.json
    -m "${model_info}"
    -o "${full_output_path}"
    predict
)

echo "即将执行以下Python命令:"
echo "--------------------------------------------------"
printf "%q " "${PYTHON_COMMAND[@]}"
printf "\n"
echo "--------------------------------------------------"
echo ""

echo "正在运行Python实验脚本..."
T_START_STEP4=$(date +%s)

"${PYTHON_COMMAND[@]}"

T_END_STEP4=$(date +%s)
echo "[步骤 4] Python脚本执行完成。"
echo "-> 耗时: $((T_END_STEP4 - T_START_STEP4)) 秒。"
echo ""

# --- 步骤 5: 打包结果文件 ---
echo "=================================================="
echo "开始打包结果文件..."
echo "目标目录: ${full_output_path}"

T_START_STEP5=$(date +%s)

archive_file_path="/root/teamshare/${output_dir}.tar"
(cd "${full_output_path}" && tar -cf "${archive_file_path}" *.csv)

T_END_STEP5=$(date +%s)
echo "[步骤 5] 打包成功！"
echo "-> 耗时: $((T_END_STEP5 - T_START_STEP5)) 秒。"
echo "归档文件已保存至: ${archive_file_path}"
echo "=================================================="
echo ""

# --- 脚本结束，计算总耗时 ---
T_END_TOTAL=$(date +%s)
echo "脚本执行完毕。所有任务已完成。"
echo "-> 总耗时: $((T_END_TOTAL - T_START_TOTAL)) 秒。"