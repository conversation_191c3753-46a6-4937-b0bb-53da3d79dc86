#%% md
# KPI 可视化看板

本 Notebook 可以让您通过输入根目录和模糊搜索模式，筛选出特定的小区（cell），并将其 `KPI.csv` 文件中的 `rrc_connmax` 指标进行可视化展示。
#%%
import os
import pandas as pd
import plotly.express as px
import ipywidgets as widgets
from IPython.display import display, clear_output
#%%
1+1
#%% md
### 步骤 1: 输入参数

- **Base Dir**: 数据所在的根目录 (例如 `sample`)。
- **Pattern**: 用于模糊匹配小区目录名称的关键字 (例如 `cell` 会匹配 `cell-1`, `cell-2` 等)。
#%%
# --- 用户输入控件 ---
base_dir_widget = widgets.Text(
    value='sample',  # 默认使用 sample 目录
    placeholder='输入数据根目录',
    description='Base Dir:',
    style={'description_width': 'initial'}
)

pattern_widget = widgets.Text(
    value='cell',  # 默认匹配 'cell'
    placeholder='输入小区名称模式',
    description='Pattern:',
    style={'description_width': 'initial'}
)

button = widgets.Button(description="生成图表")
output_area = widgets.Output()

display(widgets.VBox([base_dir_widget, pattern_widget, button, output_area]))
#%% md
### 步骤 2: 运行与分析

点击上方的 **"生成图表"** 按钮。代码将执行以下操作：
1. 在指定的 `Base Dir` 中查找所有名称包含 `Pattern` 的子目录。
2. 读取每个子目录下的 `KPI.csv` 文件。
3. 将 `rrc_connmax` 数据提取出来，并以时间为横轴绘制成线图。
4. 图表会显示在下方，您可以进行交互式操作（缩放、平移、查看具体数值）。
#%%
def plot_kpi(b):
    with output_area:
        clear_output(wait=True)
        base_dir = base_dir_widget.value
        pattern = pattern_widget.value

        # 检查根目录是否存在
        if not os.path.isdir(base_dir):
            print(f"错误: 目录 '{base_dir}' 不存在，请检查路径。")
            return

        # 1. 模糊匹配过滤目录
        try:
            all_subdirs = [d for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d))]
            matched_dirs = [d for d in all_subdirs if pattern in d]
        except Exception as e:
            print(f"错误: 遍历目录时出错: {e}")
            return

        if not matched_dirs:
            print(f"在 '{base_dir}' 中没有找到名称包含 '{pattern}' 的小区目录。")
            return

        print(f"成功找到以下匹配的小区: {matched_dirs}")

        # 2. 读取并合并所有匹配的 KPI 数据
        all_kpi_data = []
        for cell_dir in matched_dirs:
            kpi_path = os.path.join(base_dir, cell_dir, 'KPI.csv')
            if os.path.exists(kpi_path):
                try:
                    df = pd.read_csv(kpi_path)
                    if 'date_time' in df.columns and 'rrc_connmax' in df.columns:
                        df['cell_id'] = cell_dir  # 添加小区标识，用于图例区分
                        all_kpi_data.append(df)
                    else:
                        print(f"警告: 文件 {kpi_path} 中缺少 'date_time' 或 'rrc_connmax' 列。")
                except Exception as e:
                    print(f"错误: 读取文件 {kpi_path} 失败: {e}")
            else:
                print(f"警告: 在目录 {cell_dir} 中未找到 KPI.csv 文件。")

        if not all_kpi_data:
            print("未能加载任何有效的 KPI 数据，无法生成图表。")
            return

        # 3. 准备绘图数据
        combined_df = pd.concat(all_kpi_data, ignore_index=True)
        combined_df['date_time'] = pd.to_datetime(combined_df['date_time'], errors='coerce')
        combined_df.dropna(subset=['date_time'], inplace=True)
        combined_df.sort_values('date_time', inplace=True)

        # 4. 绘制图表
        fig = px.line(
            combined_df,
            x='date_time',
            y='rrc_connmax',
            color='cell_id',  # 使用 cell_id 区分不同小区的曲线
            title=f'RRC 连接数 (rrc_connmax) - 匹配模式 "{pattern}"',
            labels={'date_time': '时间', 'rrc_connmax': 'RRC 最大连接数', 'cell_id': '小区ID'},
            markers=True,
            template='plotly_white'
        )
        fig.show()

# 将绘图函数绑定到按钮的点击事件
button.on_click(plot_kpi)