#%% md
# 单个小区 KPI 可视化

本 Notebook 用于读取指定路径下的 `KPI.csv` 文件，并绘制 `rrc_connmax` 指标随时间变化的图表。
#%%
import os
import pandas as pd
import plotly.express as px
#%% md
### 步骤 1: 定义文件路径

下面的代码单元格中定义了要分析的 `KPI.csv` 文件的完整路径。
#%%
# --- 文件路径定义 ---
kpi_file_path = '../sample/0a8edc9f-91bb-349f-b569-1bd756411a88/KPI.csv'
cell_id = os.path.basename(os.path.dirname(kpi_file_path))
#%% md
### 步骤 2: 读取数据并绘图

运行以下代码将执行：
1. 使用 `pandas` 读取 CSV 文件。
2. 将 `date_time` 列转换为标准时间格式。
3. 使用 `plotly` 绘制 `rrc_connmax` 的交互式线图。
#%%
if not os.path.exists(kpi_file_path):
    print(f"错误: 文件未找到，请检查路径: {kpi_file_path}")
else:
    try:
        # 读取数据
        df = pd.read_csv(kpi_file_path)

        # 检查必需的列是否存在
        if 'date_time' not in df.columns or 'rrc_connmean' not in df.columns:
            print("错误: CSV 文件中缺少 'date_time' 或 'rrc_connmean' 列。")
        else:
            # 数据处理
            df['date_time'] = pd.to_datetime(df['date_time'], errors='coerce')
            df.dropna(subset=['date_time'], inplace=True)
            df.sort_values('date_time', inplace=True)

            # 绘制图表
            fig = px.line(
                df,
                x='date_time',
                y='rrc_connmean',
                title=f'RRC 连接数 (rrc_connmean) - 小区: {cell_id}',
                labels={'date_time': '时间', 'rrc_connmean': 'RRC 连接数'},
                markers=True,
                template='plotly_white'
            )
            fig.show()
            
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
#%%

#%%

#%%

#%%
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go

# 1. 创建模拟数据框 (长格式)
data = {
    '日期': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04',
                           '2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04',
                           '2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04']),
    '类别': ['A', 'A', 'A', 'A', 'B', 'B', 'B', 'B', 'C', 'C', 'C', 'C'],
    '值': [10, 12, 15, 13, 5, 7, 8, 9, 8, 9, 11, 10]
}
df = pd.DataFrame(data)

print("数据框示例:")
print(df)
print("\n" + "="*30 + "\n")

# # ---
#
# ## 使用 Plotly Express 绘制
#
# Plotly Express 是 Plotly 的高级API，非常适合快速创建常见的图表。它会自动处理多条曲线的绘制，你只需要指定 `color` 或 `line_dash` 参数。
#
# ```python
fig_px = px.line(df,
                 x='日期',
                 y='值',
                 color='类别', # 关键参数：根据'类别'列的值来区分不同的线条
                 title='Plotly Express: 不同类别的趋势图')

fig_px.show()
#%%
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go

# 1. 创建模拟数据框 (宽格式)
data = {
    '日期': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04']),
    '值_A': [10, 12, 15, 13],
    '值_B': [5, 7, 8, 9],
    '值_C': [8, 9, 11, 10]
}
df_wide = pd.DataFrame(data)

print("宽格式数据框示例:")
print(df_wide)
#%%
fig_px_multi_y = px.line(df_wide,
                         x='日期',
                         y=['值_A', '值_B', '值_C'], # 关键参数：将所有需要绘制的列作为列表传递给y
                         title='Plotly Express: 多列绘制多曲线 (直接指定y轴多列)')

fig_px_multi_y.show()
#%%
