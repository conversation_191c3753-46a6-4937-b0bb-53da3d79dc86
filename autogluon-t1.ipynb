#%%
import pandas as pd
from autogluon.timeseries import TimeSeriesDataFrame, TimeSeriesPredictor
#%%
from aiops_challenge_2025 import config
from aiops_challenge_2025.data import DataHelper, fillna, resample_data
from aiops_challenge_2025.experiment import (
    DATA_INTERVAL,
    PREDICT_NUM,
    PREDICT_WINDOW,
    ModelInterface,
    ChangeInfo,
    prev_quarter_time,
)
#%%
helper = DataHelper(
            data_dirs=['./sample'],
            feature_groups=[
                config.FEATURE_GROUP_KPI,
            ]
        )
#%%
helper.cells
#%%
all_dfs = []
for cell in helper.cells:
    df = helper.get(cell).get(config.FEATURE_GROUP_KPI).astype(float)
    df.reset_index(inplace=True)
    df['cell_id'] = cell
    # df = fillna(df)
    # df = resample_data(df)
    all_dfs.append(df)

all_df = pd.concat(all_dfs)

#%%
all_df.head()
#%%
all_df.dtypes
#%%
train_data = TimeSeriesDataFrame.from_data_frame(
    all_df,
    id_column="cell_id",
    timestamp_column="date_time",
)
#%%
train_data.head()
#%%
t1 = train_data.reset_index()
# group by item_id and count
t1.groupby('item_id').count()
#%%
predictor = TimeSeriesPredictor(
    prediction_length=4 * 24,
    path="sample-autopluon",
    target="rrc_connmax",
    eval_metric="SMAPE",
)
#%%
predictor.fit(
    train_data,
    presets="best_quality",
    time_limit=600,
)
#%%
predictions = predictor.predict(train_data)
#%%
predictions
#%%
