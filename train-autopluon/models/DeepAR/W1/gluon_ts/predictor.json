{"__kind__": "instance", "args": [], "class": "gluonts.torch.model.predictor.PyTorchPredictor", "kwargs": {"batch_size": 64, "device": "auto", "forecast_generator": {"__kind__": "instance", "args": [], "class": "gluonts.model.forecast_generator.SampleForecastGenerator", "kwargs": {}}, "input_names": ["feat_static_cat", "feat_static_real", "past_time_feat", "past_target", "past_observed_values", "future_time_feat"], "input_transform": {"__kind__": "instance", "class": "gluonts.transform._base.Chain", "kwargs": {"transformations": [{"__kind__": "instance", "args": [], "class": "gluonts.transform.field.RemoveFields", "kwargs": {"field_names": ["feat_static_real", "feat_dynamic_real"]}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.field.SetField", "kwargs": {"output_field": "feat_static_cat", "value": [0]}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.field.SetField", "kwargs": {"output_field": "feat_static_real", "value": [0.0]}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.convert.AsNumpyArray", "kwargs": {"dtype": {"__kind__": "type", "class": "builtins.int"}, "expected_ndim": 1, "field": "feat_static_cat"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.convert.AsNumpyArray", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "expected_ndim": 1, "field": "feat_static_real"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.convert.AsNumpyArray", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "expected_ndim": 1, "field": "target"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.feature.AddObservedValuesIndicator", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "imputation_method": {"__kind__": "instance", "args": [], "class": "gluonts.transform.feature.DummyValueImputation", "kwargs": {"dummy_value": 0.0}}, "output_field": "observed_values", "target_field": "target"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.feature.AddTimeFeatures", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "output_field": "time_feat", "pred_length": 96, "start_field": "start", "target_field": "target", "time_features": [{"__kind__": "type", "class": "autogluon.timeseries.utils.datetime.time_features.minute_of_hour"}, {"__kind__": "type", "class": "autogluon.timeseries.utils.datetime.time_features.hour_of_day"}, {"__kind__": "type", "class": "autogluon.timeseries.utils.datetime.time_features.day_of_week"}, {"__kind__": "type", "class": "autogluon.timeseries.utils.datetime.time_features.day_of_month"}, {"__kind__": "type", "class": "autogluon.timeseries.utils.datetime.time_features.day_of_year"}]}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.feature.AddAgeFeature", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "log_scale": true, "output_field": "feat_dynamic_age", "pred_length": 96, "target_field": "target"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.convert.VstackFeatures", "kwargs": {"drop_inputs": true, "h_stack": false, "input_fields": ["time_feat", "feat_dynamic_age"], "output_field": "time_feat"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.convert.AsNumpyArray", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "expected_ndim": 2, "field": "time_feat"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.split.InstanceSplitter", "kwargs": {"dummy_value": 0.0, "forecast_start_field": "forecast_start", "future_length": 96, "instance_sampler": {"__kind__": "instance", "class": "gluonts.transform.sampler.PredictionSplitSampler", "kwargs": {"allow_empty_interval": false, "axis": -1, "min_future": 0, "min_past": 0}}, "is_pad_field": "is_pad", "lead_time": 0, "output_NTC": true, "past_length": 864, "start_field": "start", "target_field": "target", "time_series_fields": ["time_feat", "observed_values"]}}]}}, "lead_time": 0, "output_transform": null, "prediction_length": 96, "prediction_net": {"__kind__": "instance", "args": [], "class": "gluonts.torch.model.deepar.lightning_module.DeepARLightningModule", "kwargs": {"lr": 0.001, "model_kwargs": {"cardinality": [1], "context_length": 192, "default_scale": null, "distr_output": {"__kind__": "instance", "args": [], "class": "gluonts.torch.distributions.studentT.StudentTOutput", "kwargs": {"beta": 0.0}}, "dropout_rate": 0.1, "embedding_dimension": null, "freq": "D", "hidden_size": 40, "lags_seq": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 95, 96, 97, 191, 192, 193, 287, 288, 289, 383, 384, 385, 479, 480, 481, 575, 576, 577, 671, 672, 673], "nonnegative_pred_samples": false, "num_feat_dynamic_real": 6, "num_feat_static_cat": 1, "num_feat_static_real": 1, "num_layers": 2, "num_parallel_samples": 100, "prediction_length": 96, "scaling": true}, "patience": 10, "weight_decay": 1e-08}}}}