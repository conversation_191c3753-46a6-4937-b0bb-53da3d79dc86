{"__kind__": "instance", "args": [], "class": "gluonts.torch.model.predictor.PyTorchPredictor", "kwargs": {"batch_size": 256, "device": "auto", "forecast_generator": {"__kind__": "instance", "args": [], "class": "gluonts.model.forecast_generator.DistributionForecastGenerator", "kwargs": {"distr_output": {"__kind__": "instance", "args": [], "class": "gluonts.torch.distributions.studentT.StudentTOutput", "kwargs": {"beta": 0.0}}}}, "input_names": ["feat_static_real", "feat_static_cat", "past_time_feat", "past_target", "past_observed_values", "future_time_feat"], "input_transform": {"__kind__": "instance", "class": "gluonts.transform._base.Chain", "kwargs": {"transformations": [{"__kind__": "instance", "args": [], "class": "gluonts.transform.field.RemoveFields", "kwargs": {"field_names": ["feat_static_real", "feat_dynamic_real"]}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.field.SetField", "kwargs": {"output_field": "feat_static_cat", "value": [0]}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.field.SetField", "kwargs": {"output_field": "feat_static_real", "value": [0.0]}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.convert.AsNumpyArray", "kwargs": {"dtype": {"__kind__": "type", "class": "builtins.int"}, "expected_ndim": 1, "field": "feat_static_cat"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.convert.AsNumpyArray", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "expected_ndim": 1, "field": "feat_static_real"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.convert.AsNumpyArray", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "expected_ndim": 1, "field": "target"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.feature.AddObservedValuesIndicator", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "imputation_method": {"__kind__": "instance", "args": [], "class": "gluonts.transform.feature.DummyValueImputation", "kwargs": {"dummy_value": 0.0}}, "output_field": "observed_values", "target_field": "target"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.feature.AddTimeFeatures", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "output_field": "time_feat", "pred_length": 96, "start_field": "start", "target_field": "target", "time_features": [{"__kind__": "type", "class": "gluonts.time_feature._base.minute_of_hour"}, {"__kind__": "type", "class": "gluonts.time_feature._base.hour_of_day"}, {"__kind__": "type", "class": "gluonts.time_feature._base.day_of_month"}, {"__kind__": "type", "class": "gluonts.time_feature._base.day_of_week"}, {"__kind__": "type", "class": "gluonts.time_feature._base.day_of_year"}, {"__kind__": "type", "class": "gluonts.time_feature._base.month_of_year"}, {"__kind__": "type", "class": "gluonts.time_feature._base.week_of_year"}]}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.convert.VstackFeatures", "kwargs": {"drop_inputs": false, "h_stack": false, "input_fields": ["time_feat"], "output_field": "time_feat"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.convert.AsNumpyArray", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "expected_ndim": 2, "field": "time_feat"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.split.InstanceSplitter", "kwargs": {"dummy_value": 0.0, "forecast_start_field": "forecast_start", "future_length": 96, "instance_sampler": {"__kind__": "instance", "class": "gluonts.transform.sampler.PredictionSplitSampler", "kwargs": {"allow_empty_interval": false, "axis": -1, "min_future": 0, "min_past": 0}}, "is_pad_field": "is_pad", "lead_time": 0, "output_NTC": true, "past_length": 192, "start_field": "start", "target_field": "target", "time_series_fields": ["time_feat", "observed_values"]}}]}}, "lead_time": 0, "output_transform": null, "prediction_length": 96, "prediction_net": {"__kind__": "instance", "args": [], "class": "gluonts.torch.model.tide.lightning_module.TiDELightningModule", "kwargs": {"lr": 0.0001, "model_kwargs": {"cardinality": [1], "context_length": 192, "decoder_hidden_dim": 256, "decoder_output_dim": 16, "distr_hidden_dim": 64, "distr_output": {"__kind__": "instance", "args": [], "class": "gluonts.torch.distributions.studentT.StudentTOutput", "kwargs": {"beta": 0.0}}, "dropout_rate": 0.2, "embedding_dimension": [16], "encoder_hidden_dim": 256, "feat_proj_hidden_dim": 4, "layer_norm": true, "num_feat_dynamic_proj": 2, "num_feat_dynamic_real": 7, "num_feat_static_cat": 1, "num_feat_static_real": 1, "num_layers_decoder": 2, "num_layers_encoder": 2, "prediction_length": 96, "scaling": "mean", "temporal_hidden_dim": 64}, "patience": 10, "weight_decay": 1e-08}}}}