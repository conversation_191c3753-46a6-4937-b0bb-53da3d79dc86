#%% md
# 单个小区 KPI 可视化

本 Notebook 用于读取指定路径下的 `KPI.csv` 文件，并绘制 `rrc_connmax` 指标随时间变化的图表。
#%%
import os
import pandas as pd
import plotly.express as px
#%% md
### 步骤 1: 定义文件路径

下面的代码单元格中定义了要分析的 `KPI.csv` 文件的完整路径。
#%%
# --- 文件路径定义 ---
kpi_file_path = '../sample/0a8edc9f-91bb-349f-b569-1bd756411a88/KPI.csv'
cell_id = os.path.basename(os.path.dirname(kpi_file_path))
#%% md
### 步骤 2: 读取数据并绘图

运行以下代码将执行：
1. 使用 `pandas` 读取 CSV 文件。
2. 将 `date_time` 列转换为标准时间格式。
3. 使用 `plotly` 绘制 `rrc_connmax` 的交互式线图。
#%%
if not os.path.exists(kpi_file_path):
    print(f"错误: 文件未找到，请检查路径: {kpi_file_path}")
else:
    try:
        # 读取数据
        df = pd.read_csv(kpi_file_path)

        # 检查必需的列是否存在
        if 'date_time' not in df.columns or 'rrc_connmax' not in df.columns:
            print("错误: CSV 文件中缺少 'date_time' 或 'rrc_connmax' 列。")
        else:
            # 数据处理
            df['date_time'] = pd.to_datetime(df['date_time'], errors='coerce')
            df.dropna(subset=['date_time'], inplace=True)
            df.sort_values('date_time', inplace=True)

            # 绘制图表
            fig = px.line(
                df,
                x='date_time',
                y='rrc_connmax',
                title=f'RRC 连接数 (rrc_connmax) - 小区: {cell_id}',
                labels={'date_time': '时间', 'rrc_connmax': 'RRC 最大连接数'},
                markers=True,
                template='plotly_white'
            )
            fig.show()
            
    except Exception as e:
        print(f"处理文件时发生错误: {e}")