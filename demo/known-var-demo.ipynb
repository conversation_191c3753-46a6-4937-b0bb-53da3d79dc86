#%%
import pandas as pd
import numpy as np
from autogluon.timeseries import TimeSeriesDataFrame, TimeSeriesPredictor
#%%

# 1. 创建模拟数据集
# 假设我们有两个商品 (item_id: A, B) 的销售数据
# 'target' 是销售量
# 'x' 是一个我们已知的、影响销售的外部因素（比如当天的广告投放量）

# 创建时间序列索引
timestamps = pd.to_datetime(pd.date_range("2023-01-01", "2023-01-20", freq="D"))
num_timestamps = len(timestamps)

# 创建数据
data_list = []
for item_id in ["A", "B"]:
    # 模拟 target 和 x 之间的强相关关系
    # 比如 x 越大，target 也越大
    x_values = np.random.randint(100, 500, size=num_timestamps)
    target_values = (x_values * np.random.uniform(0.8, 1.2, size=num_timestamps) +
                     np.random.randint(-50, 50, size=num_timestamps) +
                     (500 if item_id == "A" else 200)) # 给不同 item 不同的基准值

    df = pd.DataFrame({
        "timestamp": timestamps,
        "item_id": item_id,
        "target": target_values,
        "x": x_values
    })
    data_list.append(df)

full_data = pd.concat(data_list)
# 确保数据类型正确
full_data['target'] = full_data['target'].astype(float)
full_data['x'] = full_data['x'].astype(float)
#%%
full_data
#%%


# 将 Pandas DataFrame 转换为 AutoGluon 的 TimeSeriesDataFrame
ts_df = TimeSeriesDataFrame.from_data_frame(
    full_data,
    id_column="item_id",
    timestamp_column="timestamp"
)

print("--- 原始数据 (部分) ---")
print(ts_df.head())
print("\n")
#%%


# 2. 划分训练数据和未来已知数据
# 我们用 2023-01-15 之前的数据进行训练
# 我们要预测未来 5 天 (到 2023-01-20) 的数据
prediction_length = 5
train_data = ts_df[ts_df.index.get_level_values('timestamp') <= "2023-01-15"]
#%%

# 这是关键一步：准备好未来 5 天的协变量 'x' 的值
# 在真实场景中，这部分数据应该是你通过其他方式（如业务计划、天气预报等）获取的
known_covariates_future = ts_df[ts_df.index.get_level_values('timestamp') > "2023-01-15"]

print("--- 训练数据 (最后几行) ---")
print(train_data.tail())
print("\n")
print("--- 用于预测的未来已知协变量 'x' ---")
print(known_covariates_future[['x']]) # 预测时只需要协变量列
print("\n")
#%%


# 3. 初始化并训练模型
# 在 fit() 中指定 'x' 是已知协变量
predictor = TimeSeriesPredictor(
    prediction_length=prediction_length,
    path="autogluon_known_covariates_demo1",
    target="target",
    eval_metric="MASE",
    known_covariates_names=['x']
)
#%%

# 训练模型，并告诉它 'x' 是一个已知协变量
predictor.fit(
    train_data,
    presets='best_quality',
    time_limit=600 # 设置一个较短的训练时间用于演示
)
#%%
known_covariates_future[['x']]
#%%


# 4. 进行预测
# 在 predict() 中传入我们准备好的未来协变量数据
predictions = predictor.predict(
    train_data,
    known_covariates=known_covariates_future[['x']] # <--- 核心参数
)

print("--- 预测结果 ---")
print(predictions)
print("\n")
#%%

# 可选：将预测结果和真实值画图对比
import matplotlib.pyplot as plt

# 提取真实值用于对比
ground_truth = ts_df[ts_df.index.get_level_values('timestamp') > "2023-01-15"]

# 绘制每个 item 的预测图
for item in predictions.index.get_level_values('item_id').unique():
    plt.figure(figsize=(12, 6))

    # 绘制历史数据
    train_data.loc[item]['target'].plot(label='Historical Data')

    # 绘制预测数据
    predictions.loc[item]['mean'].plot(label='Predicted Mean', linestyle='--')

    # 绘制真实数据
    ground_truth.loc[item]['target'].plot(label='Ground Truth', linestyle=':')

    plt.title(f'Prediction vs Ground Truth for item_id: {item}')
    plt.legend()
    plt.show()
#%%
