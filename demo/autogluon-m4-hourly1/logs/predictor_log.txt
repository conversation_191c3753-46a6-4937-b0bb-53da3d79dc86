Beginning AutoGluon training... Time limit = 600s
AutoGluon will save models to 'C:\work\aiops-challenge-2025-master\demo\autogluon-m4-hourly1'
=================== System Info ===================
AutoGluon Version:  1.3.1
Python Version:     3.12.9
Operating System:   Windows
Platform Machine:   AMD64
Platform Version:   10.0.26100
CPU Count:          8
GPU Count:          0
Memory Avail:       10.13 GB / 31.96 GB (31.7%)
Disk Space Avail:   632.40 GB / 930.71 GB (67.9%)
===================================================
Setting presets to: medium_quality

Fitting with arguments:
{'enable_ensemble': True,
 'eval_metric': MASE,
 'hyperparameters': 'light',
 'known_covariates_names': [],
 'num_val_windows': 1,
 'prediction_length': 48,
 'quantile_levels': [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9],
 'random_seed': 123,
 'refit_every_n_windows': 1,
 'refit_full': False,
 'skip_model_selection': False,
 'target': 'target',
 'time_limit': 600,
 'verbosity': 2}

Inferred time series frequency: 'h'
Provided train_data has 148060 rows, 200 time series. Median time series length is 700 (min=700, max=960). 

Provided data contains following columns:
	target: 'target'
	past_covariates:
		categorical:        []
		continuous (float): ['x']

To learn how to fix incorrectly inferred types, please see documentation for TimeSeriesPredictor.fit

AutoGluon will gauge predictive performance using evaluation metric: 'MASE'
	This metric's sign has been flipped to adhere to being higher_is_better. The metric score can be multiplied by -1 to get the metric value.
===================================================

Starting training. Start time is 2025-07-12 14:02:03
Models that will be trained: ['Naive', 'SeasonalNaive', 'RecursiveTabular', 'DirectTabular', 'ETS', 'Theta', 'Chronos[bolt_small]', 'TemporalFusionTransformer']
Training timeseries model Naive. Training for up to 66.6s of the 599.8s of remaining time.
	-6.6629       = Validation score (-MASE)
	0.21    s     = Training runtime
	0.22    s     = Validation (prediction) runtime
Training timeseries model SeasonalNaive. Training for up to 74.9s of the 599.4s of remaining time.
	-1.2169       = Validation score (-MASE)
	0.21    s     = Training runtime
	0.24    s     = Validation (prediction) runtime
Training timeseries model RecursiveTabular. Training for up to 85.6s of the 598.9s of remaining time.
	-0.9339       = Validation score (-MASE)
	18.58   s     = Training runtime
	3.60    s     = Validation (prediction) runtime
Training timeseries model DirectTabular. Training for up to 96.1s of the 576.7s of remaining time.
	-1.2921       = Validation score (-MASE)
	17.56   s     = Training runtime
	0.66    s     = Validation (prediction) runtime
Training timeseries model ETS. Training for up to 111.7s of the 558.5s of remaining time.
	-1.9659       = Validation score (-MASE)
	0.22    s     = Training runtime
	41.60   s     = Validation (prediction) runtime
Training timeseries model Theta. Training for up to 129.2s of the 516.7s of remaining time.
	-2.1426       = Validation score (-MASE)
	0.21    s     = Training runtime
	2.62    s     = Validation (prediction) runtime
Training timeseries model Chronos[bolt_small]. Training for up to 171.3s of the 513.8s of remaining time.
	Warning: Exception caused Chronos[bolt_small] to fail during training... Skipping this model.
	We couldn't connect to 'https://huggingface.co' to load this file, couldn't find it in the cached files and it looks like autogluon/chronos-bolt-small is not the path to a directory containing a file named config.json.
Checkout your internet connection or see how to run the library in offline mode at 'https://huggingface.co/docs/transformers/installation#offline-mode'.
Training timeseries model TemporalFusionTransformer. Training for up to 256.6s of the 513.2s of remaining time.
