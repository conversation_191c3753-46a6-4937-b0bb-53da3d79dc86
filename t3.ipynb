#%%
import pandas as pd
#%%
df1 = pd.read_csv('./Track2/train_merged_mr.csv')
#%%
df1.head()
#%%
cells = df1.cell_id.unique()
#%%
df1['cell_id'].unique()
#%%
cells
#%%
from aiops_challenge_2025 import config
from aiops_challenge_2025.data import DataHel<PERSON>, fillna, resample_data
from aiops_challenge_2025.experiment import (
    DATA_INTERVAL,
    PREDICT_NUM,
    PREDICT_WINDOW,
    ModelInterface,
    ChangeInfo,
    prev_quarter_time,
)
#%%
data_helper2 = DataHelper(
            data_dirs=['Track2/train'],
        )
#%%
data_helper2.cells.__len__()
#%%
mr_cgi_nc_set = set(df1.cgi_nc.unique())
#%%
mr_cgi_nc_set.__class__()

#%%
data_helper2.cells.intersection(mr_cgi_nc_set)
#%%
data_helper2.cells.intersection(cells).__len__()
#%%
cells
#%%
len(cells)
#%%
cells = set(cells)
#%%
len(cells)
#%%
data_helper2.cells.intersection(cells).__len__()
#%%
(data_helper2.cells- cells).__len__()
#%%
miss_cells = (data_helper2.cells- cells)
#%%
miss_cells
#%%
data_helper2.get('ffdb2961-120f-3159-928a-5dee49907357').get(config.FEATURE_GROUP_MR)
#%%
cells & mr_cgi_nc_set
#%%
# save df1 to parquet file

df1.to_parquet('./Track2/train_merged_mr.parquet')
#%%
import pyarrow
#%%
df2 = pd.read_csv('./Track2/train_merged_pmrelation.csv')
#%%
df2.head()
#%%
df2.cell_id.unique().__len__()
#%%
len(df2)
#%%
df2.cgi_nc.unique().__len__()
#%%
mr_cgi_nc_set.__len__()

#%%
pm_cgi_nc_set = set(df2.cgi_nc.unique())
#%%
pm_cgi_nc_set.intersection(mr_cgi_nc_set).__len__()
#%%
cell_set = data_helper2.cells
#%%
pm_cgi_nc_set.intersection(cell_set)
#%%
cell_set.intersection(pm_cgi_nc_set)
cell_set.intersection(mr_cgi_nc_set)
#%%
(mr_cgi_nc_set & pm_cgi_nc_set).__len__()
#%%
len(mr_cgi_nc_set)
#%%
len(pm_cgi_nc_set)
#%%

#%%
df2.to_csv('./Track2/train/PMRelation.csv')
#%%
df2 = pd.read_csv('./Track2/train_merged_pmrelation.csv')
#%%
#view df2 memory cost
df2.info(verbose=True)
#%%
# df2 memory real cost
df2.memory_usage(deep=True).sum()
#%%
df2.to_parquet('./Track2/train_merged_pmrelation.parquet')
#
#%%
