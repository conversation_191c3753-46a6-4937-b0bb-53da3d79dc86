[build-system]
requires = ["setuptools>=70.1"]
build-backend = "setuptools.build_meta"

[project]
name = "aiops-challenge-2025"
description = "AIOps Challenge 2025 开发评测框架"
license = "Apache-2.0"
readme = "README.md"
version = "0.0.4"
requires-python = "==3.12.9"
dependencies = [
  "autogluon>=1.3.1",
  "ipywidgets>=8.1.7",
  "jupyterlab>=4.4.4",
  "numpy",
  "pandas",
  "pip>=25.1.1",
  "plotly>=6.2.0",
  "prophet>=1.1.7",
  "pyarrow>=20.0.0",
  "PyYAML",
]

[tool.setuptools]
include-package-data = true

[tool.setuptools.packages.find]
include = ["aiops_challenge_2025", "aiops_challenge_2025.*"]

[tool.setuptools.package-data]
"aiops_challenge_2025.config" = ["*.yaml"]
"aiops_challenge_2025.sample" = ["*.csv", "*.json", "**/*.csv"]
