#%%
import pandas as pd
import numpy as np
#%%
from aiops_challenge_2025 import config
from aiops_challenge_2025.data import DataHelper, fillna, resample_data
from aiops_challenge_2025.experiment import (
    DATA_INTERVAL,
    PREDICT_NUM,
    PREDICT_WINDOW,
    ModelInterface,
    ChangeInfo,
    prev_quarter_time,
)
#%%
data_path = '../Track2/train'
#%%
helper = DataHelper(
            data_dirs=[data_path],
            feature_groups=[
                config.FEATURE_GROUP_KPI,
            ]
        )
#%%
cell_id =
#%%
cell_kpi = helper.get('0a8edc9f-91bb-349f-b569-1bd756411a88').get(config.FEATURE_GROUP_KPI).astype(float)
#%%
cell_kpi.head()
#%%
cell_kpi_normalized  = (cell_kpi - cell_kpi.mean()) / cell_kpi.std()
#%%
cell_kpi_normalized.fillna(0, inplace=True)
cell_kpi_normalized.head()
#%%
# cell_kpi_long = cell_kpi.melt(value_vars=cell_kpi.columns, var_name='kpi', value_name='value')
# cell_kpi_long
#%%
import plotly.express as px
import plotly.graph_objects as go
#%%

fig_multi_kpi = px.line(cell_kpi,
                         x='date_time',
                         y=['值_A', '值_B', '值_C'], # 关键参数：将所有需要绘制的列作为列表传递给y
                         title=f'{}')

# fig_px_multi_y.show()
#%%

#%%

#%%

#%%
