lr: 0.001
model_kwargs:
  cardinality:
  - 1
  context_length: 10
  default_scale: null
  distr_output: !!python/object/apply:copyreg.__newobj_ex__
    args:
    - !!python/name:gluonts.torch.distributions.studentT.StudentTOutput ''
    - !!python/tuple []
    - &id001 !!python/object/apply:collections.OrderedDict
      - - - beta
          - 0.0
    state:
      __init_args__: *id001
      beta: 0.0
  dropout_rate: 0.1
  embedding_dimension: null
  freq: D
  hidden_size: 40
  lags_seq:
  - 1
  - 2
  - 3
  - 4
  - 5
  - 6
  - 7
  - 8
  - 13
  - 14
  - 15
  - 20
  - 21
  - 22
  - 27
  - 28
  - 29
  - 30
  - 31
  - 56
  - 84
  - 363
  - 364
  - 365
  - 727
  - 728
  - 729
  - 1091
  - 1092
  - 1093
  nonnegative_pred_samples: false
  num_feat_dynamic_real: 5
  num_feat_static_cat: 1
  num_feat_static_real: 1
  num_layers: 2
  num_parallel_samples: 100
  prediction_length: 5
  scaling: true
patience: 10
weight_decay: 1.0e-08
