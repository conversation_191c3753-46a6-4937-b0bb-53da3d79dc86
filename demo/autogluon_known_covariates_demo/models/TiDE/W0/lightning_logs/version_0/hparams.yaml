lr: 0.0001
model_kwargs:
  cardinality:
  - 1
  context_length: 64
  decoder_hidden_dim: 256
  decoder_output_dim: 16
  distr_hidden_dim: 64
  distr_output: !!python/object/apply:copyreg.__newobj_ex__
    args:
    - !!python/name:gluonts.torch.distributions.studentT.StudentTOutput ''
    - !!python/tuple []
    - &id001 !!python/object/apply:collections.OrderedDict
      - - - beta
          - 0.0
    state:
      __init_args__: *id001
      beta: 0.0
  dropout_rate: 0.2
  embedding_dimension:
  - 16
  encoder_hidden_dim: 256
  feat_proj_hidden_dim: 4
  layer_norm: true
  num_feat_dynamic_proj: 2
  num_feat_dynamic_real: 8
  num_feat_static_cat: 1
  num_feat_static_real: 1
  num_layers_decoder: 2
  num_layers_encoder: 2
  prediction_length: 5
  scaling: mean
  temporal_hidden_dim: 64
patience: 10
weight_decay: 1.0e-08
