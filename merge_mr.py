import pandas as pd
import os
import argparse
from pathlib import Path

def merge_mr_files(input_dir, output_file):
    """
    Merges all MR.csv files from subdirectories of a given directory into a single CSV file.

    Args:
        input_dir (str): The path to the directory containing cell subdirectories.
        output_file (str): The path to the output CSV file.
    """
    input_path = Path(input_dir)
    output_path = Path(output_file)

    if not input_path.is_dir():
        print(f"Error: Input directory '{input_dir}' not found.")
        return

    all_dfs = []
    
    # Iterate over each item in the input directory
    for cell_dir in input_path.iterdir():
        if cell_dir.is_dir():
            mr_file = cell_dir / 'MR.csv'
            if mr_file.is_file():
                try:
                    # Read the MR.csv file
                    df = pd.read_csv(mr_file)
                    # Add the cell_id column
                    df['cell_id'] = cell_dir.name
                    all_dfs.append(df)
                    print(f"Processed '{mr_file}'")
                except Exception as e:
                    print(f"Could not process file {mr_file}: {e}")

    if not all_dfs:
        print("No 'MR.csv' files found in any subdirectories.")
        return

    # Concatenate all dataframes
    merged_df = pd.concat(all_dfs, ignore_index=True)

    # Reorder columns to have 'cell_id' first
    cols = ['cell_id'] + [col for col in merged_df.columns if col != 'cell_id']
    merged_df = merged_df[cols]

    # Save the merged dataframe to the output file
    output_path.parent.mkdir(parents=True, exist_ok=True)
    merged_df.to_csv(output_path, index=False)
    print(f"\nSuccessfully merged {len(all_dfs)} MR files into '{output_path}'")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Merge MR.csv files from subdirectories into a single file.")
    parser.add_argument('--input-dir', type=str, required=True, help='Directory containing the cell subdirectories.')
    parser.add_argument('--output-file', type=str, required=True, help='Path for the output merged CSV file.')
    
    args = parser.parse_args()
    
    merge_mr_files(args.input_dir, args.output_file)
