Beginning AutoGluon training... Time limit = 21600s
AutoGluon will save models to 'C:\work\aiops-challenge-2025-master\train-autopluon'
=================== System Info ===================
AutoGluon Version:  1.3.1
Python Version:     3.12.9
Operating System:   Windows
Platform Machine:   AMD64
Platform Version:   10.0.26100
CPU Count:          8
GPU Count:          0
Memory Avail:       11.45 GB / 31.96 GB (35.8%)
Disk Space Avail:   640.38 GB / 930.71 GB (68.8%)
===================================================
Setting presets to: best_quality

Fitting with arguments:
{'enable_ensemble': True,
 'eval_metric': SMAPE,
 'hyperparameters': 'default',
 'known_covariates_names': [],
 'num_val_windows': 2,
 'prediction_length': 96,
 'quantile_levels': [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9],
 'random_seed': 123,
 'refit_every_n_windows': 1,
 'refit_full': False,
 'skip_model_selection': False,
 'target': 'rrc_connmax',
 'time_limit': 21600,
 'verbosity': 2}

Inferred time series frequency: '15min'
Provided train_data has 1920000 rows, 1000 time series. Median time series length is 1920 (min=1920, max=1920). 

Provided data contains following columns:
	target: 'rrc_connmax'
	past_covariates:
		categorical:        []
		continuous (float): ['ho_succoutinterenbs1', 'ho_succoutinterenbx2', 'ho_succoutintraenb', 'ho_attoutinterenbs1', 'ho_attoutinterenbx2', 'ho_attoutintraenb', ...]

AutoGluon will ignore following non-numeric/non-informative columns:
	ignored covariates:      ['drop_rate', 'pag_pagdiscarded', 'pdcch_cce_rate', 'rru_totalprbusagemeandl', 'rru_totalprbusagemeanul']

To learn how to fix incorrectly inferred types, please see documentation for TimeSeriesPredictor.fit

AutoGluon will gauge predictive performance using evaluation metric: 'SMAPE'
	This metric's sign has been flipped to adhere to being higher_is_better. The metric score can be multiplied by -1 to get the metric value.
===================================================

Starting training. Start time is 2025-07-11 00:05:08
Models that will be trained: ['SeasonalNaive', 'RecursiveTabular', 'DirectTabular', 'NPTS', 'DynamicOptimizedTheta', 'AutoETS', 'ChronosZeroShot[bolt_base]', 'ChronosFineTuned[bolt_small]', 'TemporalFusionTransformer', 'DeepAR', 'PatchTST', 'TiDE']
Training timeseries model SeasonalNaive. Training for up to 1749.3s of the 21591.9s of remaining time.
	-0.2537       = Validation score (-SMAPE)
	13.76   s     = Training runtime
	2.45    s     = Validation (prediction) runtime
Training timeseries model RecursiveTabular. Training for up to 1906.9s of the 21575.6s of remaining time.
	-0.2479       = Validation score (-SMAPE)
	1159.65 s     = Training runtime
	46.57   s     = Validation (prediction) runtime
Training timeseries model DirectTabular. Training for up to 1976.9s of the 20369.3s of remaining time.
	-0.2384       = Validation score (-SMAPE)
	771.34  s     = Training runtime
	21.69   s     = Validation (prediction) runtime
Training timeseries model NPTS. Training for up to 2108.5s of the 19576.2s of remaining time.
	-0.3670       = Validation score (-SMAPE)
	364.13  s     = Training runtime
	371.38  s     = Validation (prediction) runtime
Training timeseries model DynamicOptimizedTheta. Training for up to 2280.1s of the 18840.6s of remaining time.
	-0.2647       = Validation score (-SMAPE)
	179.68  s     = Training runtime
	178.84  s     = Validation (prediction) runtime
Training timeseries model AutoETS. Training for up to 2554.6s of the 18482.0s of remaining time.
	Time limit exceeded... Skipping AutoETS.
Training timeseries model ChronosZeroShot[bolt_base]. Training for up to 2554.0s of the 15924.3s of remaining time.
	Warning: Exception caused ChronosZeroShot[bolt_base] to fail during training... Skipping this model.
	We couldn't connect to 'https://huggingface.co' to load this file, couldn't find it in the cached files and it looks like autogluon/chronos-bolt-base is not the path to a directory containing a file named config.json.
Checkout your internet connection or see how to run the library in offline mode at 'https://huggingface.co/docs/transformers/installation#offline-mode'.
Training timeseries model ChronosFineTuned[bolt_small]. Training for up to 3062.6s of the 15912.9s of remaining time.
	Skipping covariate_regressor since the dataset contains no covariates or static features.
	Warning: Exception caused ChronosFineTuned[bolt_small] to fail during training... Skipping this model.
	We couldn't connect to 'https://huggingface.co' to load this file, couldn't find it in the cached files and it looks like autogluon/chronos-bolt-small is not the path to a directory containing a file named config.json.
Checkout your internet connection or see how to run the library in offline mode at 'https://huggingface.co/docs/transformers/installation#offline-mode'.
Training timeseries model TemporalFusionTransformer. Training for up to 3827.5s of the 15910.0s of remaining time.
	-0.2321       = Validation score (-SMAPE)
	3034.95 s     = Training runtime
	21.46   s     = Validation (prediction) runtime
Training timeseries model DeepAR. Training for up to 4084.5s of the 12853.5s of remaining time.
	-0.2301       = Validation score (-SMAPE)
	973.93  s     = Training runtime
	83.57   s     = Validation (prediction) runtime
Training timeseries model PatchTST. Training for up to 5597.9s of the 11795.9s of remaining time.
	-0.2355       = Validation score (-SMAPE)
	374.43  s     = Training runtime
	5.07    s     = Validation (prediction) runtime
Training timeseries model TiDE. Training for up to 10816.3s of the 11416.3s of remaining time.
	-0.2299       = Validation score (-SMAPE)
	2347.88 s     = Training runtime
	7.94    s     = Validation (prediction) runtime
Fitting simple weighted ensemble.
	Ensemble weights: {'DeepAR': 0.34, 'DirectTabular': 0.11, 'DynamicOptimizedTheta': 0.06, 'NPTS': 0.01, 'PatchTST': 0.2, 'RecursiveTabular': 0.06, 'TemporalFusionTransformer': 0.2, 'TiDE': 0.01}
	-0.2204       = Validation score (-SMAPE)
	28.11   s     = Training runtime
	736.51  s     = Validation (prediction) runtime
Training complete. Models trained: ['SeasonalNaive', 'RecursiveTabular', 'DirectTabular', 'NPTS', 'DynamicOptimizedTheta', 'TemporalFusionTransformer', 'DeepAR', 'PatchTST', 'TiDE', 'WeightedEnsemble']
Total runtime: 12566.75 s
Best model: WeightedEnsemble
Best model score: -0.2204
Model not specified in predict, will default to the model with the best validation score: WeightedEnsemble
