{"__kind__": "instance", "args": [], "class": "gluonts.torch.model.predictor.PyTorchPredictor", "kwargs": {"batch_size": 64, "device": "auto", "forecast_generator": {"__kind__": "instance", "args": [], "class": "gluonts.model.forecast_generator.QuantileForecastGenerator", "kwargs": {"quantiles": ["0.1", "0.2", "0.3", "0.4", "0.5", "0.6", "0.7", "0.8", "0.9"]}}, "input_names": ["past_target", "past_observed_values", "feat_static_real", "feat_static_cat", "feat_dynamic_real", "feat_dynamic_cat", "past_feat_dynamic_real", "past_feat_dynamic_cat"], "input_transform": {"__kind__": "instance", "class": "gluonts.transform._base.Chain", "kwargs": {"transformations": [{"__kind__": "instance", "args": [], "class": "gluonts.transform.field.RemoveFields", "kwargs": {"field_names": ["feat_static_real", "past_feat_dynamic_real", "feat_static_cat", "feat_dynamic_cat", "past_feat_dynamic_cat"]}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.convert.AsNumpyArray", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "expected_ndim": 1, "field": "target"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.feature.AddObservedValuesIndicator", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "imputation_method": {"__kind__": "instance", "args": [], "class": "gluonts.transform.feature.DummyValueImputation", "kwargs": {"dummy_value": 0.0}}, "output_field": "observed_values", "target_field": "target"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.feature.AddTimeFeatures", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "output_field": "time_feat", "pred_length": 5, "start_field": "start", "target_field": "target", "time_features": [{"__kind__": "type", "class": "autogluon.timeseries.utils.datetime.time_features.day_of_week"}, {"__kind__": "type", "class": "autogluon.timeseries.utils.datetime.time_features.day_of_month"}, {"__kind__": "type", "class": "autogluon.timeseries.utils.datetime.time_features.day_of_year"}]}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.field.SetField", "kwargs": {"output_field": "feat_static_real", "value": [0.0]}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.convert.AsNumpyArray", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.float32"}, "expected_ndim": 1, "field": "feat_static_real"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.field.SetField", "kwargs": {"output_field": "feat_static_cat", "value": [0]}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.convert.AsNumpyArray", "kwargs": {"dtype": {"__kind__": "type", "class": "numpy.int64"}, "expected_ndim": 1, "field": "feat_static_cat"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.convert.VstackFeatures", "kwargs": {"drop_inputs": true, "h_stack": false, "input_fields": ["time_feat", "feat_dynamic_real"], "output_field": "feat_dynamic_real"}}, {"__kind__": "instance", "args": [], "class": "gluonts.transform.split.TFTInstanceSplitter", "kwargs": {"dummy_value": 0.0, "forecast_start_field": "forecast_start", "future_length": 5, "instance_sampler": {"__kind__": "instance", "class": "gluonts.transform.sampler.PredictionSplitSampler", "kwargs": {"allow_empty_interval": false, "axis": -1, "min_future": 0, "min_past": 0}}, "is_pad_field": "is_pad", "lead_time": 0, "observed_value_field": "observed_values", "output_NTC": true, "past_length": 64, "past_time_series_fields": [], "start_field": "start", "target_field": "target", "time_series_fields": ["feat_dynamic_real"]}}]}}, "lead_time": 0, "output_transform": null, "prediction_length": 5, "prediction_net": {"__kind__": "instance", "args": [], "class": "gluonts.torch.model.tft.lightning_module.TemporalFusionTransformerLightningModule", "kwargs": {"lr": 0.001, "model_kwargs": {"c_feat_dynamic_cat": [], "c_feat_static_cat": [1], "c_past_feat_dynamic_cat": [], "context_length": 64, "d_feat_dynamic_real": [1, 1, 1, 1], "d_feat_static_real": [1], "d_hidden": 32, "d_past_feat_dynamic_real": [], "d_var": 32, "distr_output": {"__kind__": "instance", "args": [], "class": "gluonts.torch.distributions.quantile_output.QuantileOutput", "kwargs": {"quantiles": [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]}}, "dropout_rate": 0.1, "num_heads": 4, "prediction_length": 5}, "patience": 10, "weight_decay": 1e-08}}}}