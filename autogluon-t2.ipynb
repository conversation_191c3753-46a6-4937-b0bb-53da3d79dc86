#%%
import pandas as pd
import pyarrow
from autogluon.timeseries import TimeSeriesDataFrame, TimeSeriesPredictor
#%%
import json
#%%
from aiops_challenge_2025 import config
from aiops_challenge_2025.data import DataHelper, fillna, resample_data
from aiops_challenge_2025.experiment import (
    DATA_INTERVAL,
    PREDICT_NUM,
    PREDICT_WINDOW,
    ModelInterface,
    ChangeInfo,
    prev_quarter_time,
)
#%%
helper = DataHelper(
            data_dirs=['./Track2/train'],
            feature_groups=[
                config.FEATURE_GROUP_KPI,
            ]
        )
#%%
helper.cells.__len__()
#%%
df = helper.get('0a8edc9f-91bb-349f-b569-1bd756411a88').get(config.FEATURE_GROUP_KPI).astype(float)
df
#%%
start = df.index.min()
#%%
end = df.index.max()
#%%
start, end
#%%
with open('./Track2/changes-test_1-predict.json', encoding="UTF-8") as obj:
    tasks = [ChangeInfo(**item) for item in json.load(obj)]
#%%
tasks
#%%
all_dfs = []
for task in tasks:
    cell = task.cell_id
    df = helper.get(cell).get(config.FEATURE_GROUP_KPI).astype(float)

    df = resample_data(df, start, end, DATA_INTERVAL)
    df.ffill(inplace=True)
    df.fillna(0, inplace=True)

    df.reset_index(inplace=True)
    df['cell_id'] = cell
    all_dfs.append(df)

all_df = pd.concat(all_dfs)
#%%
all_df.groupby('cell_id').count()
#%%
all_df[all_df['cell_id']=='0460f4e2-e74d-335d-ac85-7275adbba57b']
#%%
all_df.to_parquet('./Track2/all_df.parquet')
#%%
helper.get('0460f4e2-e74d-335d-ac85-7275adbba57b').get(config.FEATURE_GROUP_KPI)
#%%
all_df
#%%
all_df.dtypes
#%%
train_data = TimeSeriesDataFrame.from_data_frame(
    all_df,
    id_column="cell_id",
    timestamp_column="date_time",
)
#%%
train_data.head()
#%%
t1 = train_data.reset_index()
# group by item_id and count
t1.groupby('item_id').count()
#%%
predictor = TimeSeriesPredictor(
    prediction_length=4 * 24,
    path="train-autopluon",
    target="rrc_connmax",
    eval_metric="SMAPE",
)
#%%
predictor.fit(
    train_data,
    presets="best_quality",
    time_limit=60 * 60 * 6,
)
#%%
predictions = predictor.predict(train_data)
#%%
predictions
#%%
predictions.to_csv("./Track2/predictions.csv")
#%%
import matplotlib.pyplot as plt
#%%

# TimeSeriesDataFrame can also be loaded directly from a file
test_data = TimeSeriesDataFrame.from_path("https://autogluon.s3.amazonaws.com/datasets/timeseries/m4_hourly_subset/test.csv")

# Plot 4 randomly chosen time series and the respective forecasts
predictor.plot(test_data, predictions, quantile_levels=[0.1, 0.9], max_history_length=200, max_num_item_ids=4);