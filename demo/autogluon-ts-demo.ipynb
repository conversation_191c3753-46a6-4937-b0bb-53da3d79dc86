#%%
import pandas as pd
from autogluon.timeseries import TimeSeriesDataFrame, TimeSeriesPredictor
#%%
df = pd.read_csv("https://autogluon.s3.amazonaws.com/datasets/timeseries/m4_hourly_subset/train.csv")
df.head()
#%%
df1 = df.copy(deep=True)
#%%
train_data = TimeSeriesDataFrame.from_data_frame(
    df,
    id_column="item_id",
    timestamp_column="timestamp"
)
train_data.head()
#%%
predictor = TimeSeriesPredictor(
    prediction_length=48,
    path="autogluon-m4-hourly",
    target="target",
    eval_metric="MASE",
)

predictor.fit(
    train_data,
    presets="medium_quality",
    time_limit=600,
)
#%%
predictions = predictor.predict(train_data)
predictions.head()
#%%
import matplotlib.pyplot as plt

# TimeSeriesDataFrame can also be loaded directly from a file
test_data = TimeSeriesDataFrame.from_path("https://autogluon.s3.amazonaws.com/datasets/timeseries/m4_hourly_subset/test.csv")

# Plot 4 randomly chosen time series and the respective forecasts
predictor.plot(test_data, predictions, quantile_levels=[0.1, 0.9], max_history_length=200, max_num_item_ids=4);
#%%
# The test score is computed using the last
# prediction_length=48 timesteps of each time series in test_data
predictor.leaderboard(test_data)
#%%
import numpy as np
#%%
# add a new column named "x" to df1, its value is a random number between 30 and 50
df1["x"] = 30 + 20 * np.random.rand(len(df1))
#%%
df1.head()
#%%
train_data1 = TimeSeriesDataFrame.from_data_frame(
    df1,
    id_column="item_id",
    timestamp_column="timestamp"
)
#%%

train_data1.head()
#%%
predictor1 = TimeSeriesPredictor(
    prediction_length=48,
    path="autogluon-m4-hourly1",
    target="target",
    eval_metric="MASE",
)

predictor1.fit(
    train_data1,
    presets="medium_quality",
    time_limit=600,
)
#%%
predictions1 = predictor1.predict(train_data1)
predictions1.head()
#%%
import matplotlib.pyplot as plt

# TimeSeriesDataFrame can also be loaded directly from a file
# test_data = TimeSeriesDataFrame.from_path("https://autogluon.s3.amazonaws.com/datasets/timeseries/m4_hourly_subset/test.csv")

# Plot 4 randomly chosen time series and the respective forecasts
predictor1.plot(test_data, predictions1, quantile_levels=[0.1, 0.9], max_history_length=200, max_num_item_ids=4);
#%%
# The test score is computed using the last
# prediction_length=48 timesteps of each time series in test_data
predictor1.leaderboard(test_data)